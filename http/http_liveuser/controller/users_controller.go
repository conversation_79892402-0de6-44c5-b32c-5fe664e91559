package controller

import (
	"errors"
	"fmt"
	"regexp"
	"strings"
	"taoqigame/com"
	"taoqigame/com_err"
	"taoqigame/com_lib/cache"
	"taoqigame/com_lib/ip"
	"taoqigame/com_lib/request"
	"taoqigame/com_lib/response"
	"taoqigame/com_util"
	"taoqigame/http/http_liveuser/logic/login"
	"taoqigame/http/http_liveuser/logic/users"
	"taoqigame/model"

	"github.com/gin-gonic/gin"
)

type UsersController struct {
}
type LoginResponse struct {
	Token string `json:"token"`
}

type SendCodeRequest struct {
	Type  int    `json:"type"` // 1:登录验证码 2:绑定支付平台验证码
	Phone string `json:"phone" binding:"required"`
}

var ResendVerificationCode cache.ICache[bool] = cache.NewLocalFreeCache[bool](1024 * 1024)

// Login godoc
// @Summary 发送验证码
// @Tags 主播系统/用户
// @Produce json
// @Accept json
// @Param data body SendCodeRequest true "发送验证码信息"
// @Success 200 {string} string "创建成功"
// @Router /liveuser/v1/send_code [post]
func (t UsersController) SendCode(gCtx *gin.Context) {
	var req, err = request.GetRequestBodyJson[SendCodeRequest](gCtx)
	if err != nil {
		response.ReturnFailure(gCtx, err)
		return
	}
	if req.Type == 0 {
		req.Type = 1
	}
	var exists bool
	if exists, err = ResendVerificationCode.Has(login.FormatSmsCacheKey(req.Phone, req.Type)); err != nil {
		response.ReturnFailure(gCtx, com_err.NewWarpErr(-1, "内部缓存异常", err))
		return
	}

	if exists {
		ttl, err := ResendVerificationCode.TTL(login.FormatSmsCacheKey(req.Phone, req.Type))
		if err != nil {
			response.ReturnFailure(gCtx, com_err.NewWarpErr(-1, "内部缓存异常", err))
			return
		}
		if ttl > 0 {
			response.ReturnFailure(gCtx, errors.New(fmt.Sprintf("请勿重复发送验证码,%d秒后重试", ttl)))
			return
		}
	}
	phone := req.Phone
	if phone == "" {
		response.ReturnFailure(gCtx, errors.New("请填写手机号"))
		return
	}
	if !regexp.MustCompile(`^1[3-9]\d{9}$`).MatchString(phone) {
		response.ReturnFailure(gCtx, errors.New("请输入正确的手机号码"))
		return
	}

	_, err = login.SendCode(phone, req.Type, model.AppSourceLiveUser)
	if err != nil {
		response.ReturnFailure(gCtx, err)
		return
	}
	ResendVerificationCode.Set(login.FormatSmsCacheKey(phone, req.Type), true, 60)
	response.ReturnSuccess(gCtx, nil)
}

type LoginByPhoneCodeRequest struct {
	Phone      string `json:"phone" binding:"required"`
	Code       string `json:"code" binding:"required"` //1144：万能短信码
	InviteCode string `json:"invite_code"`             //邀请码
}

// @Summary 手机号登录
// @Tags 主播系统/用户
// @Produce json
// @Accept json
// @Param data body LoginByPhoneCodeRequest true "登录信息"
// @Success 200 {object} LoginResponse "Code:-1003 表示用户不存在，需要运营添加账号"
// @Router /liveuser/v1/login [post]
func (t UsersController) LoginByPhoneCode(gCtx *gin.Context) {
	req, err := request.GetRequestBodyJson[LoginByPhoneCodeRequest](gCtx)
	if err != nil {
		response.ReturnFailure(gCtx, err)
		return
	}
	var parnetAnchor *model.Anchor
	if req.InviteCode != "" {
		parnetAnchor, err = users.GetAnchorByInviteCode(gCtx.Request.Context(), req.InviteCode)
		if err != nil {
			response.ReturnFailure(gCtx, err)
			return
		}
	}
	token, err := users.LoginByPhoneCode(gCtx.Request.Context(), req.Phone, req.Code, model.AppSourceLiveUser, parnetAnchor.Id)
	if err != nil {
		response.ReturnFailure(gCtx, err)
		return
	}
	userId, appSource, businessID, err := users.GetUserLoginTokenValue(token)
	if err != nil {
		response.ReturnFailure(gCtx, err)
		return
	}
	gCtx.Set("user_id", userId)
	gCtx.Set("app_source", appSource)
	gCtx.Set("business_id", businessID)
	response.ReturnSuccess(gCtx, LoginResponse{Token: token}, "登录成功")
}

// @Summary 退出登录
// @Tags 主播系统/用户
// @Produce json
// @Accept json
// @Success 200 {string} string ""
// @Router /liveuser/v1/logout [post]
func (t UsersController) Logout(gCtx *gin.Context) {
	tokenStr := com_util.GetRequestToken(gCtx)
	if tokenStr == "" {
		response.ReturnFailure(gCtx, errors.New("请先登录"))
		return
	}

	com.LiveUserLogoutCache.Set(tokenStr, true, 60*60*24*3)
	response.ReturnSuccess(gCtx, nil, "退出成功")
}

type UserDetailResponse struct {
	AnchorID      string `json:"anchor_id"`      // 主播ID
	Name          string `json:"name"`           // 主播名称
	Platforms     []int  `json:"platforms"`      // 平台
	Phone         string `json:"phone"`          // 手机号
	Score         string `json:"score"`          // 主播评分
	IsPay         bool   `json:"is_pay"`         // 是否付费
	IsGuide       bool   `json:"is_guide"`       // 是否引导
	IP            string `json:"ip"`             // IP归属地
	Location      string `json:"location"`       // 归属地
	ApplyNum      int64  `json:"apply_num"`      // 报名数
	PendingNum    int64  `json:"pending_num"`    // 审核中
	PassNum       int64  `json:"pass_num"`       // 入选数
	RejectNum     int64  `json:"reject_num"`     // 已驳回
	CompleteNum   int64  `json:"complete_num"`   // 完单数
	CompleteRate  int64  `json:"complete_rate"`  // 完单率
	TotalDuration int64  `json:"total_duration"` // 总时长
	TotalIncome   string `json:"total_income"`   // 收入金额
	RealName      string `json:"real_name"`      // 真实姓名
	IdNo          string `json:"id_no"`          // 身份证号码
	IsBindWechat  bool   `json:"bind_wechat"`    // 是否绑定微信号 true:是 false:否
	Wechat        string `json:"wechat"`         // 微信号
	IsShow        bool   `json:"is_show"`        // 是否出镜
	Categories    []int  `json:"categories"`     // 擅长游戏类型

}

// 用户详情
// @Summary 用户详情
// @Tags 主播系统/用户
// @Produce json
// @Success 200 {object} UserDetailResponse "返回用户信息"
// @Router /liveuser/v1/user/info [get]
func (t UsersController) UserDetail(gCtx *gin.Context) {
	ipStr := gCtx.ClientIP()
	if ipStr == "::1" {
		ipStr = "127.0.0.1"
	}
	location, _ := ip.Location(ipStr)

	anchorID := login.GetLoginUser(gCtx).AnchorID
	res, err := users.UserDetail(gCtx.Request.Context(), anchorID)
	if err != nil {
		response.ReturnFailure(gCtx, err)
		return
	}
	response.ReturnSuccess(gCtx, &UserDetailResponse{
		AnchorID:      res.AnchorID,
		Name:          res.Name,
		Platforms:     res.Platforms,
		Phone:         res.Phone,
		Score:         res.Score,
		IsPay:         res.IsPay,
		IsGuide:       res.IsGuide,
		IP:            ipStr,
		Location:      location,
		ApplyNum:      res.ApplyNum,
		PendingNum:    res.PendingNum,
		RejectNum:     res.RejectNum,
		PassNum:       res.PassNum,
		CompleteNum:   res.CompleteNum,
		CompleteRate:  res.CompleteRate,
		TotalDuration: res.TotalDuration,
		TotalIncome:   res.TotalIncome.ToYuanString(),
		RealName:      res.RealName,
		IdNo:          res.IdNo,
		IsBindWechat:  res.IsBindWechat,
		Wechat:        res.Wechat,
		IsShow:        res.IsShow,
		Categories:    res.Categories,
	}, "获取成功")
}

type SaveAuthInfoRequest struct {
	RealName string `json:"real_name"` // 真实姓名
	IDCard   string `json:"id_card"`   // 身份证号
	Wechat   string `json:"wechat"`    // 微信号
	// OrderNo   string          `json:"order_no"`  // 订单号,认证付费生成二维码接口会返回一个订单号
	Category  []int           `json:"category"`  // 游戏类型 1.FPS射击 2.MMORPG 3.即时战略 4.传奇怀旧 5.大型3D网游 6.ARPG 7.国风仙侠 8.模拟经营 9.卡牌策略 10.二次元游戏 11.MOBA 12.恋爱 13.乙女游戏 14.棋牌麻将 15.SLG
	IsShow    bool            `json:"is_show"`   // 是否出镜
	Platforms []*UserPlatform `json:"platforms"` // 平台
}

type UserPlatform struct {
	Platform   int      `json:"platform"`    // 平台 1:抖音 2:快手 3:视频号 4:虎牙
	Account    string   `json:"account"`     // 账号
	AccountImg []string `json:"account_img"` // 账号图片
}

// 引导认证页资料保存接口
// @Summary 引导认证页资料保存接口
// @Tags 主播系统/用户
// @Produce json
// @Accept json
// @Param data body SaveAuthInfoRequest true "保存认证信息"
// @Success 200 {string} string "返回用户信息"
// @Router /liveuser/v1/user/save_auth_info [post]
func (t UsersController) SaveAuthInfo(gCtx *gin.Context) {
	var req, err = request.GetRequestBodyJson[SaveAuthInfoRequest](gCtx)
	if err != nil {
		response.ReturnFailure(gCtx, err)
		return
	}
	if err := validatorSaveAuthInfo(&req); err != nil {
		response.ReturnFailure(gCtx, err)
		return
	}
	// var valid bool
	// valid, err = pay.OrderValid(gCtx.Request.Context(), req.OrderNo, &pay.OrderRel{
	// 	Table: model.NewAnchor().TableName(),
	// 	Id:    login.GetLoginUser(gCtx).UserId,
	// })
	// if err != nil {
	// 	err = com_err.NewWarpErr(com_err.ErrCodeRequestParam, "订单验证失败", err)
	// 	response.ReturnFailure(gCtx, err)
	// 	return
	// }
	// if !valid {
	// 	err = com_err.NewSysErr(com_err.ErrCodeRequestParam, "请先提交认证费")
	// 	response.ReturnFailure(gCtx, err)
	// 	return
	// }
	var logicReq = &users.SaveAuthInfoRequest{
		RealName: req.RealName,
		IDCard:   req.IDCard,
		Wechat:   req.Wechat,
		Category: req.Category,
		IsShow:   req.IsShow,
	}
	for _, platform := range req.Platforms {
		logicReq.Platforms = append(logicReq.Platforms, &users.UserPlatform{
			Platform:   platform.Platform,
			Account:    platform.Account,
			AccountImg: platform.AccountImg,
		})
	}
	err = users.SaveAuthInfo(gCtx.Request.Context(), login.GetLoginUser(gCtx).AnchorID, logicReq)
	if err != nil {
		response.ReturnFailure(gCtx, err)
		return
	}
	// pay.InvalidOrder(gCtx.Request.Context(), req.OrderNo, login.GetLoginUser(gCtx).UserId)
	// model.NewAnchor().UpdateExpireTime(gCtx.Request.Context(), nil, login.GetLoginUser(gCtx).UserId)

	response.ReturnSuccess(gCtx, nil, "保存成功")
}
func validatorSaveAuthInfo(req *SaveAuthInfoRequest) error {
	if len(req.Platforms) == 0 {
		return errors.New("请至少填写1个平台")
	}
	var validImageNums = func(platformName string) error {
		if len(req.Platforms) > 3 {
			return com_err.NewSysErr(com_err.ErrCodeRequestParam, fmt.Sprintf("%s账号截图不能超过3张", platformName))
		}
		return nil
	}
	if len(req.Category) == 0 {
		return com_err.NewSysErr(com_err.ErrCodeRequestParam, "请选择游戏类型")
	}
	if len(req.Category) > 3 {
		return com_err.NewSysErr(com_err.ErrCodeRequestParam, "请选择1-3个游戏类型")
	}
	for _, platform := range req.Platforms {
		switch platform.Platform {
		case model.PlatformTypeDouyin:
			if platform.Account == "" {
				return com_err.NewSysErr(com_err.ErrCodeRequestParam, "请填写抖音账号")
			}
			//抖音号：8位数字
			// if !regexp.MustCompile(`^\d{8}$`).MatchString(platform.Account) {
			// 	return com_err.NewSysErr(com_err.ErrCodeRequestParam, "请填写正确的抖音账号")
			// }
			if err := validImageNums("抖音"); err != nil {
				return err
			}
		case model.PlatformTypeKuaishou:
			if platform.Account == "" {
				return com_err.NewSysErr(com_err.ErrCodeRequestParam, "请填写快手账号")
			}
			//快手号：9位数字
			// if !regexp.MustCompile(`^\d{9}$`).MatchString(platform.Account) {
			// 	return com_err.NewSysErr(com_err.ErrCodeRequestParam, "请填写正确的快手账号")
			// }
			if err := validImageNums("快手"); err != nil {
				return err
			}
		case model.PlatformTypeVideo:
			if platform.Account == "" {
				return com_err.NewSysErr(com_err.ErrCodeRequestParam, "请填写视频号账号")
			}
			//视频号ID：15位字母数字
			// if !regexp.MustCompile(`^[a-zA-Z0-9]{15}$`).MatchString(platform.Account) {
			// 	return com_err.NewSysErr(com_err.ErrCodeRequestParam, "请填写正确的视频号账号")
			// }
			if err := validImageNums("视频号"); err != nil {
				return err
			}
		case model.PlatformTypeHuya:
			if platform.Account == "" {
				return com_err.NewSysErr(com_err.ErrCodeRequestParam, "请填写虎牙账号")
			}
			//虎牙号：hy_8位数字
			// if !regexp.MustCompile(`^hy_\d{8}$`).MatchString(platform.Account) {
			// 	return com_err.NewSysErr(com_err.ErrCodeRequestParam, "请填写正确的虎牙账号")
			// }
			if err := validImageNums("虎牙"); err != nil {
				return err
			}
		}
	}
	return nil
}

type AddPlatformRequest struct {
	Platform   int      `json:"platform"`    // 平台 1:抖音 2:快手 3:视频号 4:虎牙
	Account    string   `json:"account"`     // 账号
	AccountImg []string `json:"account_img"` // 账号图片
}

// 添加平台
// @Summary 添加平台
// @Tags 主播系统/用户
// @Produce json
// @Accept json
// @Param data body AddPlatformRequest true "添加平台"
// @Success 200 {string} string "新增成功"
// @Router /liveuser/v1/user/platform [post]
func (t UsersController) AddPlatform(gCtx *gin.Context) {
	var req, err = request.GetRequestBodyJson[AddPlatformRequest](gCtx)
	if err != nil {
		response.ReturnFailure(gCtx, err)
		return
	}
	err = users.AddPlatform(gCtx.Request.Context(), login.GetLoginUser(gCtx).AnchorID, &users.AddPlatformRequest{
		Platform:   req.Platform,
		Account:    req.Account,
		AccountImg: req.AccountImg,
	})
	if err != nil {
		response.ReturnFailure(gCtx, err)
		return
	}
	response.ReturnSuccess(gCtx, nil, "新增成功")
}

type AnchorPlatformsResponse struct {
	Id            int      `json:"id"`             // 主键ID
	Platform      int      `json:"platform"`       // 平台
	Account       string   `json:"account"`        // 账号
	Status        int      `json:"status"`         // 状态
	Time          int64    `json:"time"`           // 时间,Status为1时，为提交时间，Status为2时，为认证时间，Status为3时，为驳回时间
	AccountImages []string `json:"account_images"` // 账号图片
	RejectReason  string   `json:"reject_reason"`  // 驳回原因
}

// 获取主播所有平台信息
// @Summary 获取主播所有平台信息
// @Description 获取主播所有平台信息
// @Tags 主播系统/用户
// @Accept json
// @Produce json
// @Success 200 {array} AnchorPlatformsResponse "主播所有平台信息"
// @Router /liveuser/v1/user/platforms [get]
func (t *UsersController) GetPlatforms(c *gin.Context) {
	var anchorID = login.GetLoginUser(c).AnchorID
	platforms, err := users.GetAnchorPlatforms(c.Request.Context(), anchorID)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	var res []*AnchorPlatformsResponse
	for _, platform := range platforms {
		res = append(res, &AnchorPlatformsResponse{
			Id:            int(platform.Id),
			Platform:      platform.Platform,
			Account:       platform.Account,
			Status:        platform.Status,
			Time:          platform.UpdatedAt.Unix(),
			RejectReason:  platform.RejectReason,
			AccountImages: strings.Split(platform.AccountImage, ","),
		})
	}
	response.ReturnSuccess(c, res, "获取主播所有平台信息成功")
}

type UpdatePlatformRequest struct {
	Id         int64    `json:"id"`          // 主键ID
	Account    string   `json:"account"`     // 账号
	AccountImg []string `json:"account_img"` // 账号图片
}

// 更新平台信息
// @Summary 更新平台信息
// @Tags 主播系统/用户
// @Produce json
// @Accept json
// @Param data body UpdatePlatformRequest true "更新平台信息"
// @Success 200 {string} string "更新成功"
// @Router /liveuser/v1/user/platform [put]
func (t UsersController) UpdatePlatform(gCtx *gin.Context) {
	var req, err = request.GetRequestBodyJson[UpdatePlatformRequest](gCtx)
	if err != nil {
		response.ReturnFailure(gCtx, err)
		return
	}
	err = users.UpdatePlatform(gCtx.Request.Context(), login.GetLoginUser(gCtx).AnchorID, &users.UpdatePlatformRequest{
		Id:         req.Id,
		Account:    req.Account,
		AccountImg: req.AccountImg,
	})
	if err != nil {
		response.ReturnFailure(gCtx, err)
		return
	}
	response.ReturnSuccess(gCtx, nil, "平台更新成功")
}

// type GetPlatformRequest struct {
// 	Id int `form:"id"` // 平台ID
// }
// type AnchorPlatformResponse struct {
// 	Id            int64    `json:"id"`             // 平台ID
// 	Platform      int      `json:"platform"`       // 平台
// 	AnchorID      string   `json:"anchor_id"`      // 主播ID
// 	Account       string   `json:"account"`        // 账号
// 	AccountImages []string `json:"account_images"` // 账号图片
// 	Status        int      `json:"status"`         // 状态
// 	RejectReason  string   `json:"reject_reason"`
// }

// // 获取主播平台信息详情
// // @Summary 获取主播平台信息详情
// // @Description 获取主播平台信息详情
// // @Tags 主播系统/用户
// // @Accept json
// // @Param id query int true "平台ID"
// // @Success 200 {object} AnchorPlatformResponse "主播平台信息"
// // @Router /liveuser/v1/user/platform [get]
// func (t *UsersController) GetPlatform(c *gin.Context) {
// 	var req, err = request.GetRequestQueryBind[GetPlatformRequest](c)
// 	if err != nil {
// 		response.ReturnFailure(c, err)
// 		return
// 	}
// 	var anchorID = login.GetLoginUser(c).AnchorID
// 	platform, err := users.GetAnchorPlatform(c.Request.Context(), &users.AnchorPlatformRequest{
// 		Id:       req.Id,
// 		AnchorID: anchorID,
// 	})
// 	if err != nil {
// 		response.ReturnFailure(c, err)
// 		return
// 	}

// 	response.ReturnSuccess(c, &users.AnchorPlatformResponse{
// 		Id:            platform.Id,
// 		Platform:      platform.Platform,
// 		AnchorID:      platform.AnchorID,
// 		Account:       platform.Account,
// 		AccountImages: platform.AccountImages,
// 		Status:        platform.Status,
// 		RejectReason:  platform.RejectReason,
// 	}, "获取主播平台信息成功")
// }

type ModifyUserInfoRequest struct {
	Wechat     string `json:"wechat"`     // 微信号
	Categories []int  `json:"categories"` // 擅长游戏类型
	IsShow     bool   `json:"is_show"`    // 是否出镜
}

// 修改用户基础信息
// @Summary 修改用户基础信息
// @Tags 主播系统/用户
// @Produce json
// @Accept json
// @Param data body ModifyUserInfoRequest true "修改用户基础信息"
// @Success 200 {string} string "修改成功"
// @Router /liveuser/v1/user/modify_info [put]
func (t UsersController) ModifyUserInfo(gCtx *gin.Context) {
	var req, err = request.GetRequestBodyJson[ModifyUserInfoRequest](gCtx)
	if err != nil {
		response.ReturnFailure(gCtx, err)
		return
	}
	err = users.ModifyUserInfo(gCtx.Request.Context(), login.GetLoginUser(gCtx).AnchorID, &users.ModifyUserInfoRequest{
		Wechat:     req.Wechat,
		Categories: req.Categories,
		IsShow:     req.IsShow,
	})
	if err != nil {
		response.ReturnFailure(gCtx, err)
		return
	}
	response.ReturnSuccess(gCtx, nil, "修改成功")
}

// GenerateInvitePoster godoc
// @Summary 生成邀请海报
// @Tags 主播系统/用户
// @Produce json
// @Success 200 {string} string "生成成功"
// @Router /liveuser/v1/user/generate_invite_poster [post]
func (t UsersController) GenerateInvitePoster(gCtx *gin.Context) {
	//err := users.GenerateInvitePoster(gCtx.Request.Context(), login.GetLoginUser(gCtx).AnchorID)
	//if err != nil {
	//	response.ReturnFailure(gCtx, err)
	//	return
	//}

	response.ReturnSuccess(gCtx, nil, "生成成功")
}
