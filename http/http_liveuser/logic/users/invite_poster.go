package users

import (
	"context"
	"fmt"
	"taoqigame/com_err"
	"taoqigame/com_lib/storage"
	"taoqigame/com_util/image"
	"taoqigame/com_util/oss"
	"taoqigame/com_util/qrcode"
	"taoqigame/com_util/random"
	"taoqigame/model"

	"gorm.io/gorm"
)

func GenerateInvitePoster(ctx context.Context, anchorID string) (posterURL string, err error) {
	var anchor *model.Anchor
	anchor, err = model.NewAnchor().First(ctx, func(tx *gorm.DB) *gorm.DB {
		return tx.Where("anchor_id = ?", anchorID)
	})
	if err != nil {
		return
	}
	if anchor.InviteCode == "" {
		anchor.InviteCode, _ = random.GenerateString(8)
	}
	err = model.NewAnchor().Updates(ctx, nil, func(tx *gorm.DB) *gorm.DB {
		return tx.Where("id = ?", anchor.Id)
	}, map[string]interface{}{
		"invite_code": anchor.InviteCode,
	})
	if err != nil {
		return
	}
	if anchor.InvitePosterURL != "" {
		return anchor.InvitePosterURL, nil
	}
	//获取海报图片地址
	backgroundCfg, err := model.NewSystemConfig().First(ctx, func(tx *gorm.DB) *gorm.DB {
		return tx.Where("mode = ?", model.AnchorInvitePosterBackground)
	})
	if err != nil {
		return
	}
	//获取邀请链接
	inviteUrlCfg, err := model.NewSystemConfig().First(ctx, func(tx *gorm.DB) *gorm.DB {
		return tx.Where("mode = ?", model.AnchorInvitePosterUrl)
	})
	if err != nil {
		return
	}
	if backgroundCfg.CharValue == "" || inviteUrlCfg.CharValue == "" {
		err = com_err.NewWarpErr(com_err.ErrCodeDbUpdate, "系统配置异常", nil)
		return
	}
	url := fmt.Sprintf(inviteUrlCfg.CharValue, anchor.InviteCode)
	backgroundUrl := backgroundCfg.CharValue
	compositeImageData, err := DrawInvitePoster(url, backgroundUrl)
	if err != nil {
		return
	}
	// 生成文件名
	fileName := fmt.Sprintf("invite_poster_%s_%s.png", anchorID, anchor.InviteCode)
	filePath := "material/anchor/invite_poster"

	// 上传到OSS
	posterURL, err = oss.UploadFileByContent(ctx, storage.Engine, filePath, fileName, compositeImageData)
	if err != nil {
		err = com_err.NewWarpErr(com_err.ErrCodeRequestParam, "上传海报失败", err)
		return
	}

	// 更新主播的海报URL
	err = model.NewAnchor().Updates(ctx, nil, func(tx *gorm.DB) *gorm.DB {
		return tx.Where("id = ?", anchor.Id)
	}, map[string]interface{}{
		"invite_poster_url": posterURL,
	})
	if err != nil {
		err = com_err.NewWarpErr(com_err.ErrCodeDbUpdate, "更新海报URL失败", err)
		return
	}

	return
}

func DrawInvitePoster(inviteUrl string, backgroundUrl string) (compositeImageData []byte, err error) {

	// 生成无边框二维码图片
	qrCodeImage, err := qrcode.GenerateQRCodeImageNoBorder(inviteUrl, 200)
	if err != nil {
		err = com_err.NewWarpErr(com_err.ErrCodeRequestParam, "生成二维码失败", err)
		return
	}

	// 将二维码合成到背景图片上，位置为(30,30)
	compositeImageData, err = image.CompositeImage(backgroundUrl, qrCodeImage, 300, 700)
	if err != nil {
		err = com_err.NewWarpErr(com_err.ErrCodeRequestParam, "合成海报失败", err)
		return
	}
	return compositeImageData, nil

}
