package image

import (
	"bytes"
	"image"
	"image/draw"
	"image/jpeg"
	"image/png"
	"net/http"
	"taoqigame/com_err"

	"golang.org/x/image/bmp"
)

// CompositeImage 将二维码合成到背景图片上
func CompositeImage(backgroundURL string, qrCodeImage image.Image, x, y int) ([]byte, error) {
	// 下载背景图片
	resp, err := http.Get(backgroundURL)
	if err != nil {
		return nil, com_err.NewWarpErr(com_err.ErrCodeRequestParam, "下载背景图片失败", err)
	}
	defer resp.Body.Close()

	// 解码背景图片
	backgroundImage, _, err := image.Decode(resp.Body)
	if err != nil {
		return nil, com_err.NewWarpErr(com_err.ErrCodeRequestParam, "解码背景图片失败", err)
	}

	// 创建新的图片
	bounds := backgroundImage.Bounds()
	newImage := image.NewRGBA(bounds)

	// 绘制背景图片
	draw.Draw(newImage, bounds, backgroundImage, image.Point{}, draw.Src)

	// 计算二维码位置
	qrBounds := qrCodeImage.Bounds()
	qrRect := image.Rect(x, y, x+qrBounds.Dx(), y+qrBounds.Dy())

	// 绘制二维码
	draw.Draw(newImage, qrRect, qrCodeImage, image.Point{}, draw.Over)

	// 编码为PNG格式
	var buf bytes.Buffer
	err = png.Encode(&buf, newImage)
	if err != nil {
		return nil, com_err.NewWarpErr(com_err.ErrCodeRequestParam, "编码合成图片失败", err)
	}

	return buf.Bytes(), nil
}

// CompositeImageWithFormat 将二维码合成到背景图片上，支持指定输出格式
func CompositeImageWithFormat(backgroundURL string, qrCodeImage image.Image, x, y int, format string) ([]byte, error) {
	// 下载背景图片
	resp, err := http.Get(backgroundURL)
	if err != nil {
		return nil, com_err.NewWarpErr(com_err.ErrCodeRequestParam, "下载背景图片失败", err)
	}
	defer resp.Body.Close()

	// 解码背景图片
	backgroundImage, _, err := image.Decode(resp.Body)
	if err != nil {
		return nil, com_err.NewWarpErr(com_err.ErrCodeRequestParam, "解码背景图片失败", err)
	}

	// 创建新的图片
	bounds := backgroundImage.Bounds()
	newImage := image.NewRGBA(bounds)

	// 绘制背景图片
	draw.Draw(newImage, bounds, backgroundImage, image.Point{}, draw.Src)

	// 计算二维码位置
	qrBounds := qrCodeImage.Bounds()
	qrRect := image.Rect(x, y, x+qrBounds.Dx(), y+qrBounds.Dy())

	// 绘制二维码
	draw.Draw(newImage, qrRect, qrCodeImage, image.Point{}, draw.Over)

	// 编码为指定格式
	var buf bytes.Buffer
	switch format {
	case "jpeg", "jpg":
		err = jpeg.Encode(&buf, newImage, &jpeg.Options{Quality: 90})
	case "png":
		err = png.Encode(&buf, newImage)
	case "bmp":
		err = bmp.Encode(&buf, newImage)
	default:
		err = png.Encode(&buf, newImage) // 默认PNG格式
	}

	if err != nil {
		return nil, com_err.NewWarpErr(com_err.ErrCodeRequestParam, "编码合成图片失败", err)
	}

	return buf.Bytes(), nil
}
