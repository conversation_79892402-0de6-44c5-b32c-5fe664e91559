package qrcode

import (
	"os"
	"testing"
)

func TestGenerateQRCodeWithBorder(t *testing.T) {
	content := "https://example.com"
	size := 200

	// 测试生成有边框的二维码
	qrWithBorder, err := GenerateQRCodeWithBorder(content, size, true)
	if err != nil {
		t.Fatalf("生成有边框二维码失败: %v", err)
	}

	// 测试生成无边框的二维码
	qrNoBorder, err := GenerateQRCodeWithBorder(content, size, false)
	if err != nil {
		t.Fatalf("生成无边框二维码失败: %v", err)
	}

	// 保存文件用于手动检查
	err = os.WriteFile("test_qr_with_border.png", qrWithBorder, 0644)
	if err != nil {
		t.Logf("保存有边框二维码文件失败: %v", err)
	}

	err = os.WriteFile("test_qr_no_border.png", qrNoBorder, 0644)
	if err != nil {
		t.Logf("保存无边框二维码文件失败: %v", err)
	}

	// 验证两个二维码的大小不同（无边框的应该更小或相同）
	if len(qrNoBorder) > len(qrWithBorder) {
		t.Errorf("无边框二维码文件大小 (%d) 不应该大于有边框二维码 (%d)", len(qrNoBorder), len(qrWithBorder))
	}

	t.Logf("有边框二维码大小: %d bytes", len(qrWithBorder))
	t.Logf("无边框二维码大小: %d bytes", len(qrNoBorder))
}

func TestGenerateQRCodeNoBorder(t *testing.T) {
	content := "https://example.com"
	size := 200

	// 测试便捷函数
	qrNoBorder, err := GenerateQRCodeNoBorder(content, size)
	if err != nil {
		t.Fatalf("生成无边框二维码失败: %v", err)
	}

	if len(qrNoBorder) == 0 {
		t.Error("生成的二维码数据为空")
	}

	t.Logf("无边框二维码大小: %d bytes", len(qrNoBorder))
}

func TestGenerateQRCodeImageNoBorder(t *testing.T) {
	content := "https://example.com"
	size := 200

	// 测试生成无边框二维码图片
	img, err := GenerateQRCodeImageNoBorder(content, size)
	if err != nil {
		t.Fatalf("生成无边框二维码图片失败: %v", err)
	}

	if img == nil {
		t.Error("生成的二维码图片为空")
	}

	bounds := img.Bounds()
	t.Logf("无边框二维码图片尺寸: %dx%d", bounds.Dx(), bounds.Dy())
}
