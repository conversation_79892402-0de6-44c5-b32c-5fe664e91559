package qrcode

import (
	"bytes"
	"image"
	"image/png"
	"taoqigame/com_err"

	"github.com/skip2/go-qrcode"
)

// GenerateQRCode 生成二维码图片
func GenerateQRCode(content string, size int) ([]byte, error) {
	return GenerateQRCodeWithBorder(content, size, true)
}

// GenerateQRCodeWithBorder 生成二维码图片，可控制是否显示边框
func GenerateQRCodeWithBorder(content string, size int, showBorder bool) ([]byte, error) {
	if size <= 0 {
		size = 200 // 默认尺寸
	}

	// 创建二维码对象
	q, err := qrcode.New(content, qrcode.Medium)
	if err != nil {
		return nil, com_err.NewWarpErr(com_err.ErrCodeRequestParam, "生成二维码失败", err)
	}

	// 设置是否禁用边框
	q.DisableBorder = !showBorder

	// 生成PNG字节数据
	qrCode, err := q.PNG(size)
	if err != nil {
		return nil, com_err.NewWarpErr(com_err.ErrCodeRequestParam, "生成二维码PNG失败", err)
	}

	return qrCode, nil
}

// GenerateQRCodeNoBorder 生成无边框二维码图片
func GenerateQRCodeNoBorder(content string, size int) ([]byte, error) {
	return GenerateQRCodeWithBorder(content, size, false)
}

// GenerateQRCodeImage 生成二维码图片并返回image.Image
func GenerateQRCodeImage(content string, size int) (image.Image, error) {
	return GenerateQRCodeImageWithBorder(content, size, true)
}

// GenerateQRCodeImageWithBorder 生成二维码图片并返回image.Image，可控制是否显示边框
func GenerateQRCodeImageWithBorder(content string, size int, showBorder bool) (image.Image, error) {
	if size <= 0 {
		size = 200 // 默认尺寸
	}

	// 创建二维码对象
	q, err := qrcode.New(content, qrcode.Medium)
	if err != nil {
		return nil, com_err.NewWarpErr(com_err.ErrCodeRequestParam, "生成二维码失败", err)
	}

	// 设置是否禁用边框
	q.DisableBorder = !showBorder

	// 生成PNG字节数据
	qrCode, err := q.PNG(size)
	if err != nil {
		return nil, com_err.NewWarpErr(com_err.ErrCodeRequestParam, "生成二维码PNG失败", err)
	}

	// 解码为image.Image
	img, err := png.Decode(bytes.NewReader(qrCode))
	if err != nil {
		return nil, com_err.NewWarpErr(com_err.ErrCodeRequestParam, "解码二维码图片失败", err)
	}

	return img, nil
}

// GenerateQRCodeImageNoBorder 生成无边框二维码图片并返回image.Image
func GenerateQRCodeImageNoBorder(content string, size int) (image.Image, error) {
	return GenerateQRCodeImageWithBorder(content, size, false)
}
